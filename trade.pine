//@version=6
indicator("vickymosafan - [Pro Edition] #2", overlay=false)

// =============================================================================
// WAVELET INDICATOR INPUTS
// =============================================================================
shortLength = input(12, "Short Scale Length", group="Wavelet Settings")
longLength = input(23, "Long Scale Length", group="Wavelet Settings")
smoothing = input(8, "Final Signal Smoothing", group="Signal Processing")
lookback = input.int(1000, "Normalization Lookback", maxval=1000, minval=1, step=1, group="Signal Processing")
atrLength = input(8, "ATR Length", group="Wavelet Settings")

// Wavelet controls
useWavelet = input.bool(true, "Enable Magical Wavelet", group="Wavelet Settings")
noiseReduction = input.bool(true, "Enable Noise Reduction", group="Wavelet Settings")
wnMin = input(1, "NoiseR Min Length", group="Wavelet Settings")
wnMax = input(2, "NoiseR Max Length", group="Wavelet Settings")

// =============================================================================
// BREAK RETEST INDICATOR INPUTS
// =============================================================================
enableBreakRetest = input.bool(true, "Enable Break Retest Analysis", group="Break Retest Settings")
brLookback = input.int(20, "Break Retest Lookback", minval=5, maxval=100, group="Break Retest Settings")
brSensitivity = input.float(0.5, "Break Sensitivity", minval=0.1, maxval=2.0, step=0.1, group="Break Retest Settings")
retestTolerance = input.float(0.2, "Retest Tolerance %", minval=0.05, maxval=1.0, step=0.05, group="Break Retest Settings")
brConfirmationBars = input.int(3, "Break Confirmation Bars", minval=1, maxval=10, group="Break Retest Settings")
retestConfirmationBars = input.int(2, "Retest Confirmation Bars", minval=1, maxval=5, group="Break Retest Settings")
maxLines = input.int(10, "Maximum Lines to Display", minval=5, maxval=50, group="Break Retest Settings")
lineExtension = input.int(20, "Line Extension (bars)", minval=5, maxval=100, group="Break Retest Settings")
showBreakLabels = input.bool(true, "Show Break Labels", group="Break Retest Settings")
showRetestBoxes = input.bool(true, "Show Retest Zones", group="Break Retest Settings")
showRetestLabels = input.bool(true, "Show Retest Labels", group="Break Retest Settings")

// =============================================================================
// ADAPTIVE NEURAL NETWORK INPUTS
// =============================================================================
enableAI = input.bool(true, "Enable AI Prediction Engine", group="AI Neural Network")
adaptivePeriod = input(50, "Adaptive Normalization Period", group="AI Neural Network")
adaptationRate = input.float(0.08, 'Neural Adaptation Rate', step=0.01, maxval=0.15, group="AI Neural Network")

// AI Parameters
momentum_period = input.int(29, "Momentum Detector Length", group="AI Features")
volatility_period = input.int(45, "Volatility Detector Length", group="AI Features")
trend_strength_period = input.int(35, "Trend Strength Length", group="AI Features")
oscillation_period = input.int(35, "Oscillation Detector Length", group="AI Features")
velocity_period = input(30, "Price Velocity Length", group="AI Features")
resistance_factor = input.float(3.2, "Dynamic Resistance Factor", step=0.1, group="AI Features")
resistance_period = input.int(2, "Resistance Detection Period", step=1, group="AI Features")

// =============================================================================
// ADDITIONAL INDICATORS INPUTS
// =============================================================================
enableMACD = input.bool(true, "Enable MACD Analysis", group="Additional Indicators")
macdFast = input.int(12, "MACD Fast Length", group="Additional Indicators")
macdSlow = input.int(26, "MACD Slow Length", group="Additional Indicators")
macdSignal = input.int(9, "MACD Signal Length", group="Additional Indicators")

enableBollinger = input.bool(true, "Enable Bollinger Bands", group="Additional Indicators")
bbLength = input.int(20, "BB Length", group="Additional Indicators")
bbMult = input.float(2.0, "BB Multiplier", group="Additional Indicators")

enableStochastic = input.bool(true, "Enable Stochastic", group="Additional Indicators")
stochK = input.int(14, "Stochastic %K", group="Additional Indicators")
stochD = input.int(3, "Stochastic %D", group="Additional Indicators")

enableWilliamsR = input.bool(true, "Enable Williams %R", group="Additional Indicators")
williamsLength = input.int(14, "Williams %R Length", group="Additional Indicators")

enableFisherTransform = input.bool(true, "Enable Fisher Transform", group="Additional Indicators")
fisherLength = input.int(10, "Fisher Transform Length", group="Additional Indicators")

// Neural Network Weights 
alpha_momentum = 1
beta_volatility = 4
gamma_trend = 1
delta_oscillation = 2
epsilon_velocity = 5
zeta_resistance = 4

// Additional weights for new indicators
theta_macd = 3
iota_bollinger = 2
kappa_stochastic = 3
lambda_williams = 2
mu_fisher = 4

// Break Retest weights
nu_break_retest = 6

// Color options
upper_col = input.color(#00ff88, "Up Color", inline = "colors", group="Display")
lower_col = input.color(#ff4466, "Down Color", inline = "colors", group="Display")
neutral_col = input.color(#ffaa00, "Neutral Color", inline = "colors", group="Display")
break_col = input.color(#00ccff, "Break Color", inline = "colors", group="Display")
retest_col = input.color(#ffcc00, "Retest Color", inline = "colors", group="Display")

// Enhanced Display Options
candleStyle = input.string("Enhanced", "Candle Style", options=["Original", "Enhanced", "Gradient"], group="Display")
showVolume = input.bool(true, "Show Volume Indication", group="Display")
showMomentum = input.bool(true, "Show Momentum Bars", group="Display")
showBreakRetest = input.bool(true, "Show Break Retest Signals", group="Display")

// =============================================================================
// HORIZONTAL REFERENCE LINES SETTINGS
// =============================================================================
showHLines = input.bool(true, "Show Horizontal Reference Lines", group="Display")
hlineColor = input.color(color.new(color.gray, 70), "Reference Line Color", group="Display")
hlineStyle = input.string("Dashed", "Reference Line Style", options=["Solid", "Dashed", "Dotted"], group="Display")

// =============================================================================
// BREAK RETEST DETECTION FUNCTIONS
// =============================================================================

// Structure detection function
detect_structure(src, len) =>
    pivot_high = ta.pivothigh(src, len, len)
    pivot_low = ta.pivotlow(src, len, len)
    [pivot_high, pivot_low]

// Break detection function
detect_break(current_price, structure_level, is_resistance, sensitivity) =>
    break_threshold = structure_level * (1 + (is_resistance ? sensitivity / 100 : -sensitivity / 100))
    break_occurred = is_resistance ? current_price > break_threshold : current_price < break_threshold
    break_occurred

// Retest detection function
detect_retest(current_price, structure_level, tolerance) =>
    retest_upper = structure_level * (1 + tolerance / 100)
    retest_lower = structure_level * (1 - tolerance / 100)
    retest_occurred = current_price >= retest_lower and current_price <= retest_upper
    retest_occurred

// =============================================================================
// BREAK RETEST CALCULATIONS WITH LINE DRAWING
// =============================================================================

// Structure detection
[pivot_high, pivot_low] = detect_structure(hlc3, brLookback)

// Track recent structure levels and lines
var float recent_resistance = na
var float recent_support = na
var bool resistance_broken = false
var bool support_broken = false
var bool resistance_retested = false
var bool support_retested = false
var int bars_since_resistance_break = 0
var int bars_since_support_break = 0
var line resistance_line = na
var line support_line = na
var int resistance_start_bar = na
var int support_start_bar = na

// Line management arrays
var array<line> resistance_lines = array.new<line>()
var array<line> support_lines = array.new<line>()

// Function to clean old lines
clean_old_lines(lines_array, max_lines) =>
    while array.size(lines_array) >= max_lines
        old_line = array.shift(lines_array)
        line.delete(old_line)

// Update structure levels and draw lines
if not na(pivot_high) and enableBreakRetest
    recent_resistance := pivot_high
    resistance_broken := false
    resistance_retested := false
    bars_since_resistance_break := 0
    resistance_start_bar := bar_index - brLookback

    // Clean old lines if needed
    clean_old_lines(resistance_lines, maxLines)

    // Delete current resistance line if exists
    if not na(resistance_line)
        line.delete(resistance_line)

    // Draw new resistance line
    resistance_line := line.new(
        x1=resistance_start_bar,
        y1=recent_resistance,
        x2=bar_index + lineExtension,
        y2=recent_resistance,
        color=color.new(color.red, 50),
        style=line.style_dashed,
        width=2,
        extend=extend.right
    )

    // Add to array for management
    array.push(resistance_lines, resistance_line)

if not na(pivot_low) and enableBreakRetest
    recent_support := pivot_low
    support_broken := false
    support_retested := false
    bars_since_support_break := 0
    support_start_bar := bar_index - brLookback

    // Clean old lines if needed
    clean_old_lines(support_lines, maxLines)

    // Delete current support line if exists
    if not na(support_line)
        line.delete(support_line)

    // Draw new support line
    support_line := line.new(
        x1=support_start_bar,
        y1=recent_support,
        x2=bar_index + lineExtension,
        y2=recent_support,
        color=color.new(color.green, 50),
        style=line.style_dashed,
        width=2,
        extend=extend.right
    )

    // Add to array for management
    array.push(support_lines, support_line)

// Break detection and line color update
if not na(recent_resistance) and not resistance_broken
    if detect_break(close, recent_resistance, true, brSensitivity)
        resistance_broken := true
        bars_since_resistance_break := 1

        // Update resistance line color to indicate break
        if not na(resistance_line)
            line.set_color(resistance_line, color.new(break_col, 30))
            line.set_style(resistance_line, line.style_solid)
            line.set_width(resistance_line, 3)

        // Add break label
        if showBreakLabels
            label.new(
                x=bar_index,
                y=recent_resistance,
                text="R-Break",
                color=color.new(break_col, 20),
                textcolor=color.white,
                style=label.style_label_down,
                size=size.small
            )
    else
        bars_since_resistance_break := 0

if not na(recent_support) and not support_broken
    if detect_break(close, recent_support, false, brSensitivity)
        support_broken := true
        bars_since_support_break := 1

        // Update support line color to indicate break
        if not na(support_line)
            line.set_color(support_line, color.new(break_col, 30))
            line.set_style(support_line, line.style_solid)
            line.set_width(support_line, 3)

        // Add break label
        if showBreakLabels
            label.new(
                x=bar_index,
                y=recent_support,
                text="S-Break",
                color=color.new(break_col, 20),
                textcolor=color.white,
                style=label.style_label_up,
                size=size.small
            )
    else
        bars_since_support_break := 0

// Update bars since break
if resistance_broken and bars_since_resistance_break > 0
    bars_since_resistance_break := bars_since_resistance_break + 1

if support_broken and bars_since_support_break > 0
    bars_since_support_break := bars_since_support_break + 1

// Retest detection and line color update
if resistance_broken and not resistance_retested and bars_since_resistance_break > brConfirmationBars
    if detect_retest(close, recent_resistance, retestTolerance)
        resistance_retested := true

        // Update resistance line color to indicate retest
        if not na(resistance_line)
            line.set_color(resistance_line, color.new(retest_col, 20))
            line.set_style(resistance_line, line.style_dotted)

        // Create retest zone box
        if showRetestBoxes
            retest_upper = recent_resistance * (1 + retestTolerance / 100)
            retest_lower = recent_resistance * (1 - retestTolerance / 100)
            retest_box = box.new(
                left=bar_index - 5,
                top=retest_upper,
                right=bar_index + 5,
                bottom=retest_lower,
                bgcolor=color.new(retest_col, 90),
                border_color=color.new(retest_col, 50),
                border_width=1
            )

        // Add retest label
        if showRetestLabels
            label.new(
                x=bar_index,
                y=recent_resistance,
                text="R-Retest",
                color=color.new(retest_col, 20),
                textcolor=color.white,
                style=label.style_label_down,
                size=size.small
            )

if support_broken and not support_retested and bars_since_support_break > brConfirmationBars
    if detect_retest(close, recent_support, retestTolerance)
        support_retested := true

        // Update support line color to indicate retest
        if not na(support_line)
            line.set_color(support_line, color.new(retest_col, 20))
            line.set_style(support_line, line.style_dotted)

        // Create retest zone box
        retest_upper = recent_support * (1 + retestTolerance / 100)
        retest_lower = recent_support * (1 - retestTolerance / 100)
        retest_box = box.new(
            left=bar_index - 5,
            top=retest_upper,
            right=bar_index + 5,
            bottom=retest_lower,
            bgcolor=color.new(retest_col, 90),
            border_color=color.new(retest_col, 50),
            border_width=1
        )

        // Add retest label
        label.new(
            x=bar_index,
            y=recent_support,
            text="S-Retest",
            color=color.new(retest_col, 20),
            textcolor=color.white,
            style=label.style_label_up,
            size=size.small
        )

// Break Retest signals
resistance_break_signal = resistance_broken and bars_since_resistance_break == brConfirmationBars
support_break_signal = support_broken and bars_since_support_break == brConfirmationBars
resistance_retest_signal = resistance_retested and resistance_retested[1] == false
support_retest_signal = support_retested and support_retested[1] == false

// Break Retest normalized signal
br_signal = 0.0
if enableBreakRetest
    br_signal := 0.0
    if resistance_break_signal
        br_signal := 1.0
    else if support_break_signal
        br_signal := -1.0
    else if resistance_retest_signal
        br_signal := 0.8
    else if support_retest_signal
        br_signal := -0.8
    else if resistance_broken and not resistance_retested
        br_signal := 0.3
    else if support_broken and not support_retested
        br_signal := -0.3

// =============================================================================
// WAVELET FUNCTIONS & CALCULATIONS
// =============================================================================
atr = ta.atr(atrLength)

// Wavelet decomposition
pi = 3.14159265359
wavelet(src, len) =>
    alpha = (1 - math.sin(2 * pi / len)) / math.cos(2 * pi / len)
    hp = 0.0
    hp := (1 - alpha/2) * (src - src[1]) + (1 - alpha) * nz(hp[1])
    ta.ema(hp, 3)

// Multi-scale decomposition with enhanced sensitivity
fastComponent = useWavelet ? wavelet(close, shortLength) : ta.ema(close, shortLength)
slowComponent = useWavelet ? wavelet(close, longLength) : ta.ema(close, longLength)
scaleRatio = longLength / shortLength
rawSignal = (fastComponent - slowComponent) * scaleRatio

// Noise reduction
wn(src) =>
    maFast = ta.ema(src, wnMin)
    maSlow = ta.ema(src, wnMax)
    noiseReduction ? (maFast + maSlow) / 2 : src

smoothedSignal = wn(rawSignal)

// Normalization with scale preservation
atrAdjustedSignal = smoothedSignal / math.max(atr * scaleRatio, 0.001)
absMax = ta.highest(math.abs(atrAdjustedSignal), lookback)
waveletNormalized = atrAdjustedSignal / math.max(absMax, 0.001)
waveletNormalized := math.min(math.max(waveletNormalized, -1), 1)

// =============================================================================
// ADDITIONAL INDICATORS CALCULATIONS
// =============================================================================

// MACD Analysis
[macdLine, signalLine, histLine] = ta.macd(close, macdFast, macdSlow, macdSignal)
macdNormalized = enableMACD ? ta.stoch(macdLine, macdLine, macdLine, 50) / 100 - 0.5 : 0

// Bollinger Bands Analysis
basis = ta.sma(close, bbLength)
dev = bbMult * ta.stdev(close, bbLength)
upper_bb = basis + dev
lower_bb = basis - dev
bb_position = enableBollinger ? (close - lower_bb) / (upper_bb - lower_bb) - 0.5 : 0

// Stochastic Analysis
k = ta.stoch(close, high, low, stochK)
d = ta.sma(k, stochD)
stochNormalized = enableStochastic ? (k / 100) - 0.5 : 0

// Williams %R Analysis
williams_r = ta.wpr(williamsLength)
williamsNormalized = enableWilliamsR ? (williams_r + 50) / 100 - 0.5 : 0

// Fisher Transform Analysis
fisher_transform = 0.0
if enableFisherTransform
    price_normalized = 0.33 * 2 * ((close - ta.lowest(close, fisherLength)) / (ta.highest(close, fisherLength) - ta.lowest(close, fisherLength)) - 0.5)
    smooth_price = ta.ema(price_normalized, 5)
    fisher_transform := 0.5 * math.log((1 + smooth_price) / (1 - smooth_price))
    fisher_transform := ta.ema(fisher_transform, 3) / 3

// =============================================================================
// ADAPTIVE NEURAL NETWORK FUNCTIONS & CALCULATIONS
// =============================================================================

// Adaptive Data Standardization Engine
standardize_data(source_data) =>
    mean_baseline = ta.sma(source_data, adaptivePeriod)
    deviation = ta.stdev(source_data, adaptivePeriod)
    standardized = (source_data - mean_baseline) / math.max(deviation, 0.0001)
    standardized

// Enhanced Neural Activation Function
neural_activation_enhanced(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, bias, w1, w2, w3, w4, w5, w6, w7, w8, w9, w10, w11, w12) =>
    neural_input = bias + w1 * f1 + w2 * f2 + w3 * f3 + w4 * f4 + w5 * f5 + w6 * f6 + w7 * f7 + w8 * f8 + w9 * f9 + w10 * f10 + w11 * f11 + w12 * f12
    activation_output = 1 / (1 + math.exp(-neural_input))
    activation_output

// Prediction Error Calculator
prediction_error(actual_target, predicted_output) =>
    safe_predicted = math.max(math.min(predicted_output, 0.9999), 0.0001)
    error = -actual_target * math.log(safe_predicted) - (1 - actual_target) * math.log(1 - safe_predicted)
    error

// Advanced Feature Engineering Module
momentum_detector = ta.rsi(close, momentum_period)
volatility_detector = ta.cci(hlc3, volatility_period)
[trend_positive, trend_negative, _] = ta.dmi(trend_strength_period, 10)

// Custom Oscillation Detector 
highest_position(length) =>
    bars_since_high = ta.highestbars(high, length)
    oscillation_up = ((length + bars_since_high) / length) * 100
    oscillation_up

lowest_position(length) =>
    bars_since_low = ta.lowestbars(low, length)
    oscillation_down = ((length + bars_since_low) / length) * 100
    oscillation_down

oscillation_up = highest_position(oscillation_period)
oscillation_down = lowest_position(oscillation_period)

// Price Velocity Analyzer
velocity_fast = ta.ema(close, velocity_period)
velocity_slow = ta.ema(close, velocity_period - 10)

// Dynamic Resistance Detection System
[resistance_level, resistance_direction] = ta.supertrend(resistance_factor, resistance_period)

// Enhanced Neural Network Weight Management System
var float bias_node = 1.0
var float learning_adj_momentum = 0.0
var float learning_adj_volatility = 0.0
var float learning_adj_trend = 0.0
var float learning_adj_oscillation = 0.0
var float learning_adj_velocity = 0.0
var float learning_adj_resistance = 0.0
var float learning_adj_macd = 0.0
var float learning_adj_bollinger = 0.0
var float learning_adj_stochastic = 0.0
var float learning_adj_williams = 0.0
var float learning_adj_fisher = 0.0
var float learning_adj_break_retest = 0.0

// Target Variable Generation 
market_direction = standardize_data(close) > 0 ? 1 : 0

// Enhanced Feature Vector Construction 
feature_momentum = momentum_detector > 50 ? 1 : 0
feature_volatility = 0.5
if ta.crossover(volatility_detector, 100)
    feature_volatility := 1
if ta.crossunder(volatility_detector, -100)
    feature_volatility := 0

feature_trend = trend_positive > trend_negative ? 1 : 0
feature_oscillation = oscillation_up > oscillation_down ? 1 : 0
feature_velocity = velocity_fast > velocity_slow ? 1 : 0
feature_resistance = resistance_direction == -1 ? 1 : 0

// Additional features
feature_macd = macdLine > signalLine ? 1 : 0
feature_bollinger = close > basis ? 1 : 0
feature_stochastic = k > d ? 1 : 0
feature_williams = williams_r > -50 ? 1 : 0
feature_fisher = fisher_transform > 0 ? 1 : 0

// Break Retest features
feature_break_retest = br_signal > 0 ? 1 : 0

// Enhanced Adaptive Neural Network Processing
ai_prediction = 0.0
if enableAI
    current_momentum = alpha_momentum + learning_adj_momentum
    current_volatility = beta_volatility + learning_adj_volatility
    current_trend = gamma_trend + learning_adj_trend
    current_oscillation = delta_oscillation + learning_adj_oscillation
    current_velocity = epsilon_velocity + learning_adj_velocity
    current_resistance = zeta_resistance + learning_adj_resistance
    current_macd = theta_macd + learning_adj_macd
    current_bollinger = iota_bollinger + learning_adj_bollinger
    current_stochastic = kappa_stochastic + learning_adj_stochastic
    current_williams = lambda_williams + learning_adj_williams
    current_fisher = mu_fisher + learning_adj_fisher
    current_break_retest = nu_break_retest + learning_adj_break_retest
    
    // Forward Pass: Generate Initial Prediction with Enhanced Features
    initial_prediction = neural_activation_enhanced(feature_momentum, feature_volatility, feature_trend, feature_oscillation, feature_velocity, feature_resistance, feature_macd, feature_bollinger, feature_stochastic, feature_williams, feature_fisher, feature_break_retest, bias_node, current_momentum, current_volatility, current_trend, current_oscillation, current_velocity, current_resistance, current_macd, current_bollinger, current_stochastic, current_williams, current_fisher, current_break_retest)
    
    // Backward Pass: Enhanced Adaptive Weight Adjustment 
    prediction_gradient = initial_prediction - market_direction
    learning_adj_momentum := learning_adj_momentum - adaptationRate * prediction_gradient * feature_momentum
    learning_adj_volatility := learning_adj_volatility - adaptationRate * prediction_gradient * feature_volatility
    learning_adj_trend := learning_adj_trend - adaptationRate * prediction_gradient * feature_trend
    learning_adj_oscillation := learning_adj_oscillation - adaptationRate * prediction_gradient * feature_oscillation
    learning_adj_velocity := learning_adj_velocity - adaptationRate * prediction_gradient * feature_velocity
    learning_adj_resistance := learning_adj_resistance - adaptationRate * prediction_gradient * feature_resistance
    learning_adj_macd := learning_adj_macd - adaptationRate * prediction_gradient * feature_macd
    learning_adj_bollinger := learning_adj_bollinger - adaptationRate * prediction_gradient * feature_bollinger
    learning_adj_stochastic := learning_adj_stochastic - adaptationRate * prediction_gradient * feature_stochastic
    learning_adj_williams := learning_adj_williams - adaptationRate * prediction_gradient * feature_williams
    learning_adj_fisher := learning_adj_fisher - adaptationRate * prediction_gradient * feature_fisher
    learning_adj_break_retest := learning_adj_break_retest - adaptationRate * prediction_gradient * feature_break_retest
    
    // Final Prediction with Enhanced Adapted Weights
    final_prediction = neural_activation_enhanced(feature_momentum, feature_volatility, feature_trend, feature_oscillation, feature_velocity, feature_resistance, feature_macd, feature_bollinger, feature_stochastic, feature_williams, feature_fisher, feature_break_retest, bias_node, current_momentum, current_volatility, current_trend, current_oscillation, current_velocity, current_resistance, current_macd, current_bollinger, current_stochastic, current_williams, current_fisher, current_break_retest)
    
    // Transform to Bipolar Signal Range (-1 to 1)
    ai_prediction := (final_prediction - 0.5) * 2

// =============================================================================
// ENHANCED SIGNAL FUSION WITH BREAK RETEST
// =============================================================================

// Calculate composite signal from additional indicators
additionalSignalsComposite = 0.0
signalCount = 0

if enableMACD
    additionalSignalsComposite += macdNormalized
    signalCount += 1

if enableBollinger
    additionalSignalsComposite += bb_position
    signalCount += 1

if enableStochastic
    additionalSignalsComposite += stochNormalized
    signalCount += 1

if enableWilliamsR
    additionalSignalsComposite += williamsNormalized
    signalCount += 1

if enableFisherTransform
    additionalSignalsComposite += fisher_transform
    signalCount += 1

if enableBreakRetest
    additionalSignalsComposite += br_signal
    signalCount += 1

additionalSignalsNormalized = signalCount > 0 ? additionalSignalsComposite / signalCount : 0

// Enhanced signal fusion
rawFinalSignal = 0.0
componentCount = 0

if useWavelet
    rawFinalSignal += waveletNormalized
    componentCount += 1

if enableAI
    rawFinalSignal += ai_prediction
    componentCount += 1

if signalCount > 0
    rawFinalSignal += additionalSignalsNormalized
    componentCount += 1

if componentCount == 0
    rawFinalSignal := ta.mom(close, 14) / close
else
    rawFinalSignal := rawFinalSignal / componentCount

// APPLY SMOOTHING TO ENTIRE FINAL SIGNAL 
smoothedFinalSignal = ta.ema(rawFinalSignal, smoothing)

// ENHANCED ADAPTIVE NORMALIZATION
recentVolatility = ta.stdev(smoothedFinalSignal, lookback)
longTermVolatility = ta.stdev(smoothedFinalSignal, lookback * 2)
adaptiveScale = recentVolatility / math.max(longTermVolatility, 0.0001)

// Apply adaptive scaling 
finalSignal = smoothedFinalSignal * adaptiveScale
finalSignal := math.min(math.max(finalSignal, -2), 2)  

// Enhanced signal strength calculation
signalStrength = math.abs(finalSignal)
signalMomentum = finalSignal - finalSignal[1]

// =============================================================================
// HORIZONTAL REFERENCE LINES
// =============================================================================

// Draw horizontal reference lines using plot method 
refColor1 = showHLines ? color.new(chart.fg_color, 80) : color.new(color.white, 100)
refColor2 = showHLines ? color.new(chart.fg_color, 50) : color.new(color.white, 100)
zeroColor = showHLines ? color.new(bar_index % 2 == 0 ? chart.fg_color : na, 0) : color.new(color.white, 100)

plot(1.5, "Extreme Bullish", color = refColor1)
plot(1.0, "Strong Bullish", color = refColor2)
plot(0.75, "Moderate Bullish", color = refColor1)
plot(0.5, "Bullish Zone", color = refColor2)
plot(0.25, "Weak Bullish", color = refColor1)
plot(-0.25, "Weak Bearish", color = refColor1)
plot(-0.5, "Bearish Zone", color = refColor2)
plot(-0.75, "Moderate Bearish", color = refColor1)
plot(-1.0, "Strong Bearish", color = refColor2)
plot(-1.5, "Extreme Bearish", color = refColor1)

// =============================================================================
// ENHANCED VISUALIZATION WITH BREAK RETEST INTEGRATION
// =============================================================================

// Dynamic color calculation based on signal strength, momentum, and break retest
dynamicColor = switch
    resistance_break_signal => color.new(break_col, 0)
    support_break_signal => color.new(break_col, 0)
    resistance_retest_signal => color.new(retest_col, 0)
    support_retest_signal => color.new(retest_col, 0)
    finalSignal > 0.75 => color.new(upper_col, 0)
    finalSignal > 0.25 => color.new(upper_col, 30)
    finalSignal > 0 => color.new(upper_col, 60)
    finalSignal > -0.25 => color.new(neutral_col, 60)
    finalSignal > -0.75 => color.new(lower_col, 60)
    => color.new(lower_col, 0)

// Volume-based sizing
volumeRatio = showVolume ? math.min(volume / ta.sma(volume, 20), 3) : 1
candleThickness = math.max(1, math.round(volumeRatio))

// Enhanced candle color calculation with break retest integration
candleColor = switch candleStyle
    "Enhanced" => 
        if resistance_break_signal or support_break_signal
            color.new(break_col, 0)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, 0)
        else
            dynamicColor
    "Gradient" => 
        if resistance_break_signal or support_break_signal
            color.new(break_col, 0)
        else if resistance_retest_signal or support_retest_signal
            color.new(retest_col, 0)
        else
            gradientIntensity = math.abs(finalSignal) * 100
            color.new(dynamicColor, 100 - gradientIntensity)
    => 
        if resistance_break_signal or support_break_signal
            break_col
        else if resistance_retest_signal or support_retest_signal
            retest_col
        else
            finalSignal >= 0 ? upper_col : lower_col

candleWickColor = candleColor
candleBorderColor = switch candleStyle
    "Enhanced" => color.new(candleColor, 20)
    "Gradient" => candleColor
    => candleColor

// Plot enhanced signal candles
plotcandle(open, high, low, close,
           title = 'Enhanced Signal Candles',
           color = candleColor,
           wickcolor = candleWickColor,
           bordercolor = candleBorderColor, 
           force_overlay=true)

// Enhanced signal plotting
mainSignalColor = switch
    resistance_break_signal or support_break_signal => color.new(break_col, 0)
    resistance_retest_signal or support_retest_signal => color.new(retest_col, 0)
    finalSignal > 1 => color.new(upper_col, 0)
    finalSignal > 0.5 => color.new(upper_col, 20)
    finalSignal > 0 => color.new(upper_col, 40)
    finalSignal > -0.5 => color.new(lower_col, 40)
    finalSignal > -1 => color.new(lower_col, 20)
    => color.new(lower_col, 0)

p1 = plot(finalSignal, "Enhanced Signal", color = mainSignalColor, linewidth = 2)
p2 = plot(0, "Zero Line", linewidth = 1, color = color.new(color.gray, 50))

// Enhanced gradient fill
fillColor = switch
    resistance_break_signal or support_break_signal => color.new(break_col, 70)
    resistance_retest_signal or support_retest_signal => color.new(retest_col, 70)
    finalSignal > 0.5 => color.new(upper_col, 80)
    finalSignal > 0 => color.new(upper_col, 85)
    finalSignal > -0.5 => color.new(lower_col, 85)
    => color.new(lower_col, 80)

fill(p1, p2, finalSignal, 0, na, fillColor)

// Momentum bars calculation
momentumColor = signalMomentum > 0 ? color.new(upper_col, 70) : color.new(lower_col, 70)
momentumValue = showMomentum ? signalMomentum * 2 : na
plot(momentumValue, "Momentum", color = momentumColor, style = plot.style_histogram, linewidth = 1)

// Enhanced signal arrows with break retest integration
strongBullishSignal = finalSignal > 0.5 and finalSignal[1] <= 0.5
weakBullishSignal = finalSignal > 0 and finalSignal[1] <= 0 and not strongBullishSignal
strongBearishSignal = finalSignal < -0.5 and finalSignal[1] >= -0.5
weakBearishSignal = finalSignal < 0 and finalSignal[1] >= 0 and not strongBearishSignal

// Break Retest specific signals for plotting
breakBullishSignal = resistance_break_signal and showBreakRetest
breakBearishSignal = support_break_signal and showBreakRetest
retestBullishSignal = resistance_retest_signal and showBreakRetest
retestBearishSignal = support_retest_signal and showBreakRetest

plotshape(strongBullishSignal, 
  style=shape.labelup, 
  location=location.belowbar, 
  color=upper_col, 
  textcolor=color.white,
  size=size.tiny,
  text="👆🏿",
  title="Strong Bullish Signal", force_overlay=true)

plotshape(weakBullishSignal, 
  style=shape.labelup, 
  location=location.belowbar, 
  color=upper_col, 
  textcolor=#000000,
  size=size.tiny,
  text="⇗",
  title="Weak Bullish Signal", force_overlay=true)

plotshape(strongBearishSignal, 
  style=shape.labeldown,
  location=location.abovebar, 
  color=lower_col, 
  textcolor=color.white,
  size=size.tiny,
  text="👇",
  title="Strong Bearish Signal", force_overlay=true)

plotshape(weakBearishSignal, 
  style=shape.labeldown,
  location=location.abovebar, 
  color=lower_col, 
  textcolor=#000000,
  size=size.tiny,
  text="⇘",
  title="Weak Bearish Signal", force_overlay=true)

// Break Retest specific shapes
plotshape(breakBullishSignal, 
  style=shape.triangleup, 
  location=location.belowbar, 
  color=break_col, 
  textcolor=color.white,
  size=size.tiny,
  text="break",
  title="Resistance Break Signal", force_overlay=true)

plotshape(breakBearishSignal, 
  style=shape.triangledown,
  location=location.abovebar, 
  color=break_col, 
  textcolor=color.white,
  size=size.tiny,
  text="break",
  title="Support Break Signal", force_overlay=true)

plotshape(retestBullishSignal, 
  style=shape.diamond, 
  location=location.belowbar, 
  color=retest_col, 
  textcolor=#c8c8c8,
  size=size.tiny,
  text="retest",
  title="Resistance Retest Signal", force_overlay=true)

plotshape(retestBearishSignal, 
  style=shape.diamond,
  location=location.abovebar, 
  color=retest_col, 
  textcolor=#c8c8c8,
  size=size.tiny,
  text="retest",
  title="Support Retest Signal", force_overlay=true)

// =============================================================================
// ENHANCED ALERT SYSTEM WITH BREAK RETEST
// =============================================================================
alertcondition(strongBullishSignal, "Strong Bullish Signal", "Strong Bullish Signal Detected - High Confidence")
alertcondition(weakBullishSignal, "Weak Bullish Signal", "Weak Bullish Signal Detected")
alertcondition(strongBearishSignal, "Strong Bearish Signal", "Strong Bearish Signal Detected - High Confidence")
alertcondition(weakBearishSignal, "Weak Bearish Signal", "Weak Bearish Signal Detected")
alertcondition(math.abs(finalSignal) > 1.5, "Extreme Signal", "Extreme Market Condition Detected")
alertcondition(ta.crossover(finalSignal, 0), "Zero Line Cross Up", "Signal Crossed Above Zero Line")
alertcondition(ta.crossunder(finalSignal, 0), "Zero Line Cross Down", "Signal Crossed Below Zero Line")

// Break Retest specific alerts
alertcondition(resistance_break_signal, "Resistance Break", "Resistance Level Broken - Bullish Breakout")
alertcondition(support_break_signal, "Support Break", "Support Level Broken - Bearish Breakdown")
alertcondition(resistance_retest_signal, "Resistance Retest", "Resistance Level Retested - Potential Reversal")
alertcondition(support_retest_signal, "Support Retest", "Support Level Retested - Potential Bounce")
alertcondition(breakBullishSignal or breakBearishSignal, "Break Signal", "Break Signal Detected - Structure Broken")
alertcondition(retestBullishSignal or retestBearishSignal, "Retest Signal", "Retest Signal Detected - Structure Retested")

// =============================================================================
// BREAK RETEST INFORMATION TABLE (OPTIONAL)
// =============================================================================
showInfoTable = input.bool(false, "Show Break Retest Info Table", group="Display")

if showInfoTable and barstate.islast
    var table infoTable = table.new(position.top_right, 2, 6, bgcolor=color.new(#ffffff, 34), border_width=1)
    
    table.cell(infoTable, 0, 0, "Break Retest Status", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 0, "", text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 1, "Resistance Level", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 1, str.tostring(recent_resistance, "#.####"), text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 2, "Support Level", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 2, str.tostring(recent_support, "#.####"), text_color=color.black, text_size=size.small)
    
    table.cell(infoTable, 0, 3, "Resistance Status", text_color=color.black, text_size=size.small)
    res_status = resistance_broken ? (resistance_retested ? "Broken & Retested" : "Broken") : "Intact"
    table.cell(infoTable, 1, 3, res_status, text_color=resistance_broken ? color.red : #407e42, text_size=size.small)
    
    table.cell(infoTable, 0, 4, "Support Status", text_color=color.black, text_size=size.small)
    sup_status = support_broken ? (support_retested ? "Broken & Retested" : "Broken") : "Intact"
    table.cell(infoTable, 1, 4, sup_status, text_color=support_broken ? color.red : #407e42, text_size=size.small)
    
    table.cell(infoTable, 0, 5, "BR Signal Strength", text_color=color.black, text_size=size.small)
    table.cell(infoTable, 1, 5, str.tostring(math.abs(br_signal), "#.##"), text_color=color.black, text_size=size.small)